/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #2563eb;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding-top: 80px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.highlight {
    color: #fbbf24;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #fbbf24;
    color: #1f2937;
}

.btn-primary:hover {
    background: #f59e0b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
}

.hero-image {
    display: flex;
    justify-content: center;
}

.profile-placeholder {
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 6rem;
    color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: #1f2937;
}

/* About Section */
.about {
    background: #f9fafb;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #4b5563;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 0.5rem;
}

.stat p {
    color: #6b7280;
    font-weight: 500;
}

/* Skills Section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.skill-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease;
}

.skill-card:hover {
    transform: translateY(-5px);
}

.skill-icon {
    font-size: 3rem;
    color: #2563eb;
    margin-bottom: 1rem;
}

.skill-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.skill-card p {
    color: #6b7280;
}

/* Portfolio Section */
.portfolio {
    background: #f9fafb;
}

.portfolio-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.portfolio-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #4b5563;
}

.portfolio-grid {
    display: grid;
    gap: 1.5rem;
}

.portfolio-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.portfolio-icon {
    font-size: 2rem;
    color: #2563eb;
}

.portfolio-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.portfolio-item p {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Values Section */
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    font-size: 3rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.value-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.value-card p {
    color: #6b7280;
}

/* Blog Section */
.blog {
    background: #f9fafb;
}

.blog-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.blog-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #4b5563;
}

.blog-topics {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.topic-tag {
    background: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Interests Section */
.interests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.interest-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease;
}

.interest-card:hover {
    transform: translateY(-5px);
}

.interest-icon {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.interest-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.interest-card p {
    color: #6b7280;
}

/* Contact Section */
.contact {
    background: #1f2937;
    color: white;
}

.contact .section-title {
    color: white;
}

.contact-content {
    text-align: center;
}

.contact-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #d1d5db;
}

.contact-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 3rem 0;
}

.contact-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    text-decoration: none;
    padding: 1rem 2rem;
    background: #374151;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.contact-link:hover {
    background: #2563eb;
    transform: translateY(-2px);
}

.contact-note {
    margin-top: 3rem;
    padding: 2rem;
    background: #374151;
    border-radius: 8px;
}

.contact-note p {
    color: #d1d5db;
    font-size: 1rem;
}

/* Footer */
.footer {
    background: #111827;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .about-content,
    .portfolio-content,
    .blog-content {
        grid-template-columns: 1fr;
    }
    
    .contact-links {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }
}
